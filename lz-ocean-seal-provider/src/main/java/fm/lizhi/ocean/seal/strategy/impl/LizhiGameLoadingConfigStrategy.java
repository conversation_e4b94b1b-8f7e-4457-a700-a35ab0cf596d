package fm.lizhi.ocean.seal.strategy.impl;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 荔枝渠道游戏加载配置策略实现
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class LizhiGameLoadingConfigStrategy implements GameLoadingConfigStrategy {

    @Override
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean) {
        try {
            log.info("Generating Lizhi game loading config for gameId: {}, channelGameId: {}", 
                    bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());
            
            // 荔枝渠道的游戏加载配置逻辑
            // 这里可以根据荔枝渠道的特定需求来构建配置
            String url = generateLizhiGameUrl(gameInfoBean);
            String gZipUrl = generateLizhiGZipUrl(gameInfoBean);
            
            if (StringUtils.isBlank(url) || StringUtils.isBlank(gZipUrl)) {
                log.error("Failed to generate Lizhi URLs, url: {}, gZipUrl: {}", url, gZipUrl);
                return null;
            }
            
            return GameServiceProto.GameLoadingConfig.newBuilder()
                    .setGameId(bizGameBean.getId())
                    .setUrl(url)
                    .setGZipUrl(gZipUrl)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error generating Lizhi game loading config for gameId: {}", bizGameBean.getId(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.LIZHI.equals(channel);
    }
    
    /**
     * 生成荔枝渠道的游戏 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 游戏 URL
     */
    private String generateLizhiGameUrl(GameInfoBean gameInfoBean) {
        // 荔枝渠道特定的 URL 生成逻辑
        // 这里可以根据实际的荔枝渠道要求来实现
        String baseUrl = "https://lizhi-games.example.com";
        return String.format("%s/games/%s/load", baseUrl, gameInfoBean.getChannelGameIdStr());
    }
    
    /**
     * 生成荔枝渠道的游戏压缩包 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 压缩包 URL
     */
    private String generateLizhiGZipUrl(GameInfoBean gameInfoBean) {
        // 荔枝渠道特定的压缩包 URL 生成逻辑
        String baseUrl = "https://lizhi-assets.example.com";
        return String.format("%s/games/%s/assets.zip", baseUrl, gameInfoBean.getChannelGameIdStr());
    }
}

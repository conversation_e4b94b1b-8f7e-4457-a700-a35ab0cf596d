package fm.lizhi.ocean.seal.strategy.example;

import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.GameLoadingConfigManager;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;

/**
 * 策略模式使用示例
 * 展示如何使用游戏加载配置策略模式
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
public class StrategyUsageExample {
    
    private GameLoadingConfigManager gameLoadingConfigManager;
    
    /**
     * 示例：根据不同渠道获取游戏加载配置
     */
    public void demonstrateStrategyUsage() {
        // 创建测试数据
        GameBizGameBean bizGameBean = createTestBizGameBean();
        
        // 测试 SUD 渠道
        GameInfoBean sudGameInfo = createTestGameInfoBean(GameChannel.SUD, "sud-game-123");
        GameServiceProto.GameLoadingConfig sudConfig = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.SUD, bizGameBean, sudGameInfo);
        System.out.println("SUD Config: " + sudConfig);
        
        // 测试 LUK 渠道
        GameInfoBean lukGameInfo = createTestGameInfoBean(GameChannel.LUK, "luk-game-456");
        GameServiceProto.GameLoadingConfig lukConfig = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.LUK, bizGameBean, lukGameInfo);
        System.out.println("LUK Config: " + lukConfig);
        
        // 测试荔枝渠道
        GameInfoBean lizhiGameInfo = createTestGameInfoBean(GameChannel.LIZHI, "lizhi-game-789");
        GameServiceProto.GameLoadingConfig lizhiConfig = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.LIZHI, bizGameBean, lizhiGameInfo);
        System.out.println("Lizhi Config: " + lizhiConfig);
        
        // 测试声网渠道
        GameInfoBean agoraGameInfo = createTestGameInfoBean(GameChannel.AGORA, "agora-game-101");
        GameServiceProto.GameLoadingConfig agoraConfig = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.AGORA, bizGameBean, agoraGameInfo);
        System.out.println("Agora Config: " + agoraConfig);
        
        // 测试未知渠道（使用默认策略）
        GameInfoBean unknownGameInfo = createTestGameInfoBean("unknown", "unknown-game-202");
        GameServiceProto.GameLoadingConfig unknownConfig = gameLoadingConfigManager.getGameLoadingConfig(
                "unknown", bizGameBean, unknownGameInfo);
        System.out.println("Unknown Channel Config: " + unknownConfig);
    }
    
    /**
     * 创建测试用的业务游戏Bean
     */
    private GameBizGameBean createTestBizGameBean() {
        GameBizGameBean bean = new GameBizGameBean();
        bean.setId(12345L);
        bean.setGameInfoId(67890L);
        bean.setConfig("{\"minPlayer\": 2, \"maxPlayer\": 4}");
        return bean;
    }
    
    /**
     * 创建测试用的游戏信息Bean
     */
    private GameInfoBean createTestGameInfoBean(String channel, String channelGameId) {
        GameInfoBean bean = new GameInfoBean();
        bean.setId(67890L);
        bean.setChannel(channel);
        bean.setChannelGameIdStr(channelGameId);
        bean.setName("Test Game");
        bean.setDesc("Test game description");
        bean.setStatus(1);
        bean.setRenderType(0);
        bean.setCaptain(1);
        return bean;
    }
    
    /**
     * 示例：检查渠道支持情况
     */
    public void demonstrateChannelSupport() {
        String[] channels = {GameChannel.SUD, GameChannel.LUK, GameChannel.LIZHI, GameChannel.AGORA, "unknown"};
        
        for (String channel : channels) {
            boolean supported = gameLoadingConfigManager.isChannelSupported(channel);
            System.out.println("Channel " + channel + " is supported: " + supported);
        }
    }
    
    /**
     * 示例：错误处理
     */
    public void demonstrateErrorHandling() {
        GameBizGameBean bizGameBean = createTestBizGameBean();
        GameInfoBean gameInfoBean = createTestGameInfoBean(GameChannel.SUD, "test-game");
        
        // 测试空渠道
        GameServiceProto.GameLoadingConfig config1 = gameLoadingConfigManager.getGameLoadingConfig(
                null, bizGameBean, gameInfoBean);
        System.out.println("Null channel result: " + config1);
        
        // 测试空业务游戏Bean
        GameServiceProto.GameLoadingConfig config2 = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.SUD, null, gameInfoBean);
        System.out.println("Null bizGameBean result: " + config2);
        
        // 测试空游戏信息Bean
        GameServiceProto.GameLoadingConfig config3 = gameLoadingConfigManager.getGameLoadingConfig(
                GameChannel.SUD, bizGameBean, null);
        System.out.println("Null gameInfoBean result: " + config3);
    }
}

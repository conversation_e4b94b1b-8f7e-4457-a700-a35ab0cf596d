package fm.lizhi.ocean.seal.strategy.impl;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.GameLoadingConfigStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * SUD 渠道游戏加载配置策略实现
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@Slf4j
@AutoBindSingleton
public class SudGameLoadingConfigStrategy implements GameLoadingConfigStrategy {

    @Override
    public GameServiceProto.GameLoadingConfig getGameLoadingConfig(GameBizGameBean bizGameBean, GameInfoBean gameInfoBean) {
        try {
            log.info("Generating SUD game loading config for gameId: {}, channelGameId: {}", 
                    bizGameBean.getId(), gameInfoBean.getChannelGameIdStr());
            
            // SUD 渠道的游戏加载配置逻辑
            // 这里可以根据 SUD 渠道的特定需求来构建配置
            String url = generateSudGameUrl(gameInfoBean);
            String gZipUrl = generateSudGZipUrl(gameInfoBean);
            
            if (StringUtils.isBlank(url) || StringUtils.isBlank(gZipUrl)) {
                log.error("Failed to generate SUD URLs, url: {}, gZipUrl: {}", url, gZipUrl);
                return null;
            }
            
            return GameServiceProto.GameLoadingConfig.newBuilder()
                    .setGameId(bizGameBean.getId())
                    .setUrl(url)
                    .setGZipUrl(gZipUrl)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error generating SUD game loading config for gameId: {}", bizGameBean.getId(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String channel) {
        return GameChannel.SUD.equals(channel);
    }
    
    /**
     * 生成 SUD 渠道的游戏 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 游戏 URL
     */
    private String generateSudGameUrl(GameInfoBean gameInfoBean) {
        // SUD 渠道特定的 URL 生成逻辑
        // 这里可以根据实际的 SUD 渠道要求来实现
        String baseUrl = "https://sud-games.example.com";
        return String.format("%s/games/%s/load", baseUrl, gameInfoBean.getChannelGameIdStr());
    }
    
    /**
     * 生成 SUD 渠道的游戏压缩包 URL
     * 
     * @param gameInfoBean 游戏信息
     * @return 压缩包 URL
     */
    private String generateSudGZipUrl(GameInfoBean gameInfoBean) {
        // SUD 渠道特定的压缩包 URL 生成逻辑
        String baseUrl = "https://sud-assets.example.com";
        return String.format("%s/games/%s/assets.zip", baseUrl, gameInfoBean.getChannelGameIdStr());
    }
}

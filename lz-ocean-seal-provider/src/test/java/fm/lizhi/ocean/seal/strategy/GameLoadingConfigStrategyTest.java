package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.strategy.impl.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * 游戏加载配置策略测试
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@RunWith(MockitoJUnitRunner.class)
public class GameLoadingConfigStrategyTest {
    
    private SudGameLoadingConfigStrategy sudStrategy;
    private LukGameLoadingConfigStrategy lukStrategy;
    private LizhiGameLoadingConfigStrategy lizhiStrategy;
    private AgoraGameLoadingConfigStrategy agoraStrategy;
    private DefaultGameLoadingConfigStrategy defaultStrategy;
    
    private GameBizGameBean bizGameBean;
    private GameInfoBean gameInfoBean;
    
    @Before
    public void setUp() {
        sudStrategy = new SudGameLoadingConfigStrategy();
        lukStrategy = new LukGameLoadingConfigStrategy();
        lizhiStrategy = new LizhiGameLoadingConfigStrategy();
        agoraStrategy = new AgoraGameLoadingConfigStrategy();
        defaultStrategy = new DefaultGameLoadingConfigStrategy();
        
        // 创建测试数据
        bizGameBean = new GameBizGameBean();
        bizGameBean.setId(12345L);
        
        gameInfoBean = new GameInfoBean();
        gameInfoBean.setChannelGameIdStr("test-game-123");
        gameInfoBean.setChannel(GameChannel.SUD);
    }
    
    @Test
    public void testSudStrategySupports() {
        assertTrue("SUD strategy should support SUD channel", sudStrategy.supports(GameChannel.SUD));
        assertFalse("SUD strategy should not support LUK channel", sudStrategy.supports(GameChannel.LUK));
    }
    
    @Test
    public void testLukStrategySupports() {
        assertTrue("LUK strategy should support LUK channel", lukStrategy.supports(GameChannel.LUK));
        assertFalse("LUK strategy should not support SUD channel", lukStrategy.supports(GameChannel.SUD));
    }
    
    @Test
    public void testLizhiStrategySupports() {
        assertTrue("Lizhi strategy should support LIZHI channel", lizhiStrategy.supports(GameChannel.LIZHI));
        assertFalse("Lizhi strategy should not support SUD channel", lizhiStrategy.supports(GameChannel.SUD));
    }
    
    @Test
    public void testAgoraStrategySupports() {
        assertTrue("Agora strategy should support AGORA channel", agoraStrategy.supports(GameChannel.AGORA));
        assertFalse("Agora strategy should not support SUD channel", agoraStrategy.supports(GameChannel.SUD));
    }
    
    @Test
    public void testDefaultStrategySupports() {
        assertTrue("Default strategy should support any channel", defaultStrategy.supports("unknown"));
        assertTrue("Default strategy should support SUD channel", defaultStrategy.supports(GameChannel.SUD));
    }
    
    @Test
    public void testSudStrategyGeneratesConfig() {
        GameServiceProto.GameLoadingConfig config = sudStrategy.getGameLoadingConfig(bizGameBean, gameInfoBean);
        
        assertNotNull("Config should not be null", config);
        assertEquals("Game ID should match", bizGameBean.getId().longValue(), config.getGameId());
        assertTrue("URL should contain game ID", config.getUrl().contains(gameInfoBean.getChannelGameIdStr()));
        assertTrue("GZip URL should contain game ID", config.getGZipUrl().contains(gameInfoBean.getChannelGameIdStr()));
    }
    
    @Test
    public void testLukStrategyGeneratesConfig() {
        gameInfoBean.setChannel(GameChannel.LUK);
        GameServiceProto.GameLoadingConfig config = lukStrategy.getGameLoadingConfig(bizGameBean, gameInfoBean);
        
        assertNotNull("Config should not be null", config);
        assertEquals("Game ID should match", bizGameBean.getId().longValue(), config.getGameId());
        assertTrue("URL should contain game ID", config.getUrl().contains(gameInfoBean.getChannelGameIdStr()));
        assertTrue("GZip URL should contain game ID", config.getGZipUrl().contains(gameInfoBean.getChannelGameIdStr()));
    }
    
    @Test
    public void testDefaultStrategyGeneratesConfig() {
        gameInfoBean.setChannel("unknown");
        GameServiceProto.GameLoadingConfig config = defaultStrategy.getGameLoadingConfig(bizGameBean, gameInfoBean);
        
        assertNotNull("Config should not be null", config);
        assertEquals("Game ID should match", bizGameBean.getId().longValue(), config.getGameId());
        assertTrue("URL should contain game ID", config.getUrl().contains(gameInfoBean.getChannelGameIdStr()));
        assertTrue("GZip URL should contain game ID", config.getGZipUrl().contains(gameInfoBean.getChannelGameIdStr()));
    }
}

package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.strategy.impl.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * 游戏加载配置策略工厂测试
 * 
 * Created in 2025-06-27
 * 
 * <AUTHOR> Agent
 */
@RunWith(MockitoJUnitRunner.class)
public class GameLoadingConfigStrategyFactoryTest {
    
    @Mock
    private SudGameLoadingConfigStrategy sudStrategy;
    
    @Mock
    private LukGameLoadingConfigStrategy lukStrategy;
    
    @Mock
    private LizhiGameLoadingConfigStrategy lizhiStrategy;
    
    @Mock
    private AgoraGameLoadingConfigStrategy agoraStrategy;
    
    @Mock
    private DefaultGameLoadingConfigStrategy defaultStrategy;
    
    @InjectMocks
    private GameLoadingConfigStrategyFactory factory;
    
    @Before
    public void setUp() {
        // 设置 mock 行为
        when(sudStrategy.supports(GameChannel.SUD)).thenReturn(true);
        when(sudStrategy.supports(GameChannel.LUK)).thenReturn(false);
        when(sudStrategy.supports(GameChannel.LIZHI)).thenReturn(false);
        when(sudStrategy.supports(GameChannel.AGORA)).thenReturn(false);
        
        when(lukStrategy.supports(GameChannel.LUK)).thenReturn(true);
        when(lukStrategy.supports(GameChannel.SUD)).thenReturn(false);
        when(lukStrategy.supports(GameChannel.LIZHI)).thenReturn(false);
        when(lukStrategy.supports(GameChannel.AGORA)).thenReturn(false);
        
        when(lizhiStrategy.supports(GameChannel.LIZHI)).thenReturn(true);
        when(lizhiStrategy.supports(GameChannel.SUD)).thenReturn(false);
        when(lizhiStrategy.supports(GameChannel.LUK)).thenReturn(false);
        when(lizhiStrategy.supports(GameChannel.AGORA)).thenReturn(false);
        
        when(agoraStrategy.supports(GameChannel.AGORA)).thenReturn(true);
        when(agoraStrategy.supports(GameChannel.SUD)).thenReturn(false);
        when(agoraStrategy.supports(GameChannel.LUK)).thenReturn(false);
        when(agoraStrategy.supports(GameChannel.LIZHI)).thenReturn(false);
        
        when(defaultStrategy.supports("unknown")).thenReturn(true);
    }
    
    @Test
    public void testGetSudStrategy() {
        GameLoadingConfigStrategy strategy = factory.getStrategy(GameChannel.SUD);
        assertEquals("Should return SUD strategy", sudStrategy, strategy);
    }
    
    @Test
    public void testGetLukStrategy() {
        GameLoadingConfigStrategy strategy = factory.getStrategy(GameChannel.LUK);
        assertEquals("Should return LUK strategy", lukStrategy, strategy);
    }
    
    @Test
    public void testGetLizhiStrategy() {
        GameLoadingConfigStrategy strategy = factory.getStrategy(GameChannel.LIZHI);
        assertEquals("Should return Lizhi strategy", lizhiStrategy, strategy);
    }
    
    @Test
    public void testGetAgoraStrategy() {
        GameLoadingConfigStrategy strategy = factory.getStrategy(GameChannel.AGORA);
        assertEquals("Should return Agora strategy", agoraStrategy, strategy);
    }
    
    @Test
    public void testGetDefaultStrategyForUnknownChannel() {
        GameLoadingConfigStrategy strategy = factory.getStrategy("unknown");
        assertEquals("Should return default strategy for unknown channel", defaultStrategy, strategy);
    }
    
    @Test
    public void testGetDefaultStrategyForBlankChannel() {
        GameLoadingConfigStrategy strategy = factory.getStrategy("");
        assertNull("Should return null for blank channel", strategy);
    }
    
    @Test
    public void testGetDefaultStrategyForNullChannel() {
        GameLoadingConfigStrategy strategy = factory.getStrategy(null);
        assertNull("Should return null for null channel", strategy);
    }
    
    @Test
    public void testGetSupportedChannels() {
        var supportedChannels = factory.getSupportedChannels();
        assertNotNull("Supported channels should not be null", supportedChannels);
        assertTrue("Should contain sud", supportedChannels.contains("sud"));
        assertTrue("Should contain luk", supportedChannels.contains("luk"));
        assertTrue("Should contain lizhi", supportedChannels.contains("lizhi"));
        assertTrue("Should contain agora", supportedChannels.contains("agora"));
    }
}
